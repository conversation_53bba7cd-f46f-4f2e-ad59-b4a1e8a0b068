# Скачать и установить Ollama с официального сайта: https://ollama.com/download
# После установки запустить Ollama
# Убедиться, что модель "gemma3:latest" загружена (или изменить код, чтобы использовать другую доступную модель)
# pip install ollama aiohttp
from datetime import datetime

import ollama
import re
import json
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
import os


async def async_ollama_generate(model: str, prompt: str) -> str:
    """
    Асинхронный запрос к Ollama API

    Args:
        model (str): Название модели
        prompt (str): Запрос к модели

    Returns:
        str: Ответ от модели
    """
    loop = asyncio.get_event_loop()
    try:
        response = await loop.run_in_executor(
            None,
            lambda: ollama.chat(
                model=model, messages=[{"role": "user", "content": prompt}]
            ),
        )
        return response["message"]["content"]
    except Exception:
        try:
            response = await loop.run_in_executor(
                None, lambda: ollama.generate(model=model, prompt=prompt)
            )
            return response["response"]
        except Exception:
            raise Exception("Не удалось получить ответ от Ollama API")


def clean_llm_json_response(response_text: str) -> Any:
    """
    Извлекает JSON из ответа LLM, убирая Markdown-разметку.

    Args:
        response_text (str): Исходный ответ от LLM, возможно содержащий Markdown-разметку

    Returns:
        dict/list: Распарсенный JSON объект
    """
    json_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", response_text)

    if json_match:
        json_str = json_match.group(1)
    else:
        json_str = response_text

    json_str = json_str.strip()

    try:
        json_obj = json.loads(json_str)
        return json_obj
    except json.JSONDecodeError as e:
        raise ValueError(f"Не удалось распарсить JSON: {e}")


async def process_single_sku(model_name: str, item: str) -> Dict[str, Any]:
    """
    Обрабатывает один SKU асинхронно

    Args:
        model_name (str): Название модели Ollama
        item (str): SKU для обработки

    Returns:
        Dict[str, Any]: Результат обработки
    """
    prompt = (
        f"""sku:{item}."""
        """Максимально точно определи и извлеки данные в формате json. Используй только данные из предложенных вариантов.
                {
                    "sku": str,  // оригинальный SKU
                    "grams_in_pcs": float,  // grams, ml
                    "pcs_in_block": float,
                    "box_in_cartoon": int,
                    "weight_unit": float, // g,ml,kg,гр,грм,кг,мл
                    "pcs_type": str, //pcs,шт
                    "box_type": str // jar, box, банка, блок
                }
            """
    )

    response_text = await async_ollama_generate(model_name, prompt)
    clean_json = clean_llm_json_response(response_text)

    return {"original_sku": item, "parsed_data": clean_json}


async def main_async(
    text: List[str], save_to_file: bool = False
) -> Optional[List[Dict[str, Any]]]:
    """
    Асинхронная основная функция для обработки списка SKU

    Args:
        text (List[str]): Список SKU для обработки
        save_to_file (bool): Сохранять результаты в файл

    Returns:
        Optional[List[Dict[str, Any]]]: Список результатов обработки или None в случае ошибки
    """
    model_name = "gemma3:latest"  # используем доступную модель

    try:
        # Создаем задачи для асинхронного выполнения
        tasks = [process_single_sku(model_name, item) for item in text]

        # Выполняем все задачи параллельно
        results = await asyncio.gather(*tasks)

        if save_to_file:
            # Сохраняем все результаты в файл
            with open("sku_results.json", "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=4)

        return results

    except Exception as e:
        import traceback

        traceback.print_exc()
        return None


def main(text: List[str], save_to_file: bool = False) -> Optional[List[Dict[str, Any]]]:
    """
    Обертка для запуска асинхронной функции

    Args:
        text (List[str]): Список SKU для обработки
        save_to_file (bool): Сохранять результаты в файл

    Returns:
        Optional[List[Dict[str, Any]]]: Список результатов обработки или None в случае ошибки
    """
    return asyncio.run(main_async(text, save_to_file))


if __name__ == "__main__":
    test_products = [
        """"Alpella" молочный шоколад с фундуком 80г*18шт*6бл/Хамле №8012""",
        """"Alpella" шоколадная палочка 41,50мл*18шт*6банк/Хамле №08021""",
        """"Alpella" шоколадная палочка 22.5г*8*16/Хамле №8020""",
    ]
    main(test_products[:3], save_to_file=True)
