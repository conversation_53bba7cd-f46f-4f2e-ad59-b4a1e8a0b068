# Сжимает PDF-файл, сохраняя его качество.

import fitz  # PyMuPDF
import os
import sys
import tempfile
from PIL import Image
import io
import argparse
import logging

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_arguments(file_source):
    """
    Парсинг аргументов командной строки.

    Returns:
        argparse.Namespace: Объект с аргументами командной строки
    """
    parser = argparse.ArgumentParser(description="Сжатие PDF-файлов")
    parser.add_argument(
        "--input",
        "-i",
        type=str,
        default=file_source,
        help="Путь к входному PDF-файлу",
    )
    parser.add_argument(
        "--output",
        "-o",
        type=str,
        default=None,
        help="Путь к выходному PDF-файлу (по умолчанию: input_compressed.pdf)",
    )
    parser.add_argument(
        "--quality",
        "-q",
        type=int,
        default=50,
        help="Качество JPEG (0-100, меньше = больше сжатие)",
    )
    parser.add_argument(
        "--dpi", "-d", type=int, default=90, help="Разрешение в DPI (точек на дюйм)"
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Подробный вывод")
    return parser.parse_args()


def get_output_filename(input_path, output_path=None):
    """
    Определяет имя выходного файла.

    Args:
        input_path (str): Путь к входному файлу
        output_path (str, optional): Путь к выходному файлу. Defaults to None.

    Returns:
        str: Путь к выходному файлу
    """
    if output_path:
        return output_path

    # Если выходной файл не указан, создаем имя на основе входного
    input_base = os.path.splitext(input_path)[0]
    return f"{input_base} .pdf"


def process_page(page, dpi, compression_quality, verbose=False):
    """
    Обрабатывает одну страницу PDF, преобразуя ее в сжатое изображение.

    Args:
        page (fitz.Page): Страница PDF
        dpi (int): Разрешение в DPI
        compression_quality (int): Качество сжатия JPEG (0-100)
        verbose (bool, optional): Подробный вывод. Defaults to False.

    Returns:
        str: Путь к временному файлу с изображением
    """
    # Рендерим страницу в изображение с заданным разрешением
    pix = page.get_pixmap(dpi=dpi)

    # Конвертируем в PIL Image
    img_data = pix.tobytes("jpeg", compression_quality)
    img = Image.open(io.BytesIO(img_data))

    # Сохраняем сжатое изображение во временный файл
    with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
        tmp_filename = tmp.name
        img.save(tmp_filename, "JPEG", quality=compression_quality, optimize=True)

    return tmp_filename


def compress_pdf(
    input_path, output_path, dpi=150, compression_quality=50, verbose=False
):
    """
    Сжимает PDF-файл.

    Args:
        input_path (str): Путь к входному PDF-файлу
        output_path (str): Путь к выходному PDF-файлу
        dpi (int, optional): Разрешение в DPI. Defaults to 150.
        compression_quality (int, optional): Качество сжатия JPEG (0-100). Defaults to 50.
        verbose (bool, optional): Подробный вывод. Defaults to False.

    Returns:
        tuple: (успех (bool), информация о сжатии (dict))
    """
    if not os.path.exists(input_path):
        return False, {}

    try:
        doc = fitz.open(input_path)
        new_doc = fitz.open()

        for page_num in range(len(doc)):
            page = doc[page_num]
            tmp_filename = process_page(page, dpi, compression_quality, verbose)
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            new_page.insert_image(new_page.rect, filename=tmp_filename)
            os.unlink(tmp_filename)

        new_doc.save(
            output_path, garbage=4, deflate=True, clean=True, pretty=False, linear=True
        )

        doc.close()
        new_doc.close()

        input_size = os.path.getsize(input_path) / 1024
        output_size = os.path.getsize(output_path) / 1024
        reduction = (1 - output_size / input_size) * 100

        return True, {
            "input_size": input_size,
            "output_size": output_size,
            "reduction": reduction,
        }

    except Exception as e:
        return False, {"error": str(e)}


def print_usage_examples():
    """
    Выводит примеры использования программы.
    """
    print("\nПримеры использования:")
    print("1. Сжатие с параметрами по умолчанию:")
    print("   python ChangePdfSize.py --input input.pdf")
    print("2. Указание выходного файла:")
    print("   python ChangePdfSize.py --input input.pdf --output output.pdf")
    print("3. Настройка качества и разрешения:")
    print("   python ChangePdfSize.py --input input.pdf --quality 70 --dpi 200")
    print("4. Использование коротких параметров:")
    print("   python ChangePdfSize.py -i input.pdf -o output.pdf -q 70 -d 200")
    print("5. Подробный вывод:")
    print("   python ChangePdfSize.py -i input.pdf -v")


def resize_pdf(file_source, delete_original=False):
    """
    Основная функция программы.
    """
    # Получаем аргументы командной строки
    args = parse_arguments(file_source)

    # Определяем пути к файлам
    input_pdf = args.input
    output_pdf = get_output_filename(input_pdf, args.output)

    # Параметры сжатия
    compression_quality = args.quality
    dpi = args.dpi
    verbose = args.verbose

    logger.info(f"Выходной файл: {output_pdf}")

    # Сжимаем PDF
    success, _ = compress_pdf(
        input_pdf,
        output_pdf,
        dpi=dpi,
        compression_quality=compression_quality,
        verbose=verbose,
    )

    if delete_original:
        os.remove(input_pdf)
        os.rename(output_pdf, input_pdf)

    return 0 if success else 1


if __name__ == "__main__":
    file_source = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\товарно-транспортна накладна 10794905 02 01 2021.pdf"
    sys.exit(resize_pdf(file_source))
