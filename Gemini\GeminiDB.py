
import google.generativeai as genai
import json
import os
from prompt import PROMPT_FLASH
from dotenv import load_dotenv
load_dotenv()

# Замените на свой API ключ Gemini
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

genai.configure(api_key=GEMINI_API_KEY)

# Выберите модель Gemini (например, "gemini-1.0-pro-latest")
# MODEL_NAME = os.getenv("GEMINI_MODEL_2F")
MODEL_NAME = "models/gemini-2.0-pro-exp-02-05"
model = genai.GenerativeModel(MODEL_NAME)


def extract_document_data(text):
    """
    Извлекает данные из текста документа, используя Gemini.

    Args:
        text: Текст документа.

    Returns:
        Словарь с извлеченными данными в формате JSON, как указано в требованиях.
    """

    prompt = PROMPT_FLASH.format(text=text)

    try:
        response = model.generate_content(prompt)
        json_string = response.text

        # Gemini может возвращать JSON с обрамляющими кавычками или с дополнительным текстом.  Удаляем их.
        json_string = json_string.strip()
        if json_string.startswith('```json'):
            json_string = json_string[7:]
        if json_string.endswith('```'):
            json_string = json_string[:-3]
        if json_string.startswith('"'):
            json_string = json_string[1:]
        if json_string.endswith('"'):
            json_string = json_string[:-1]


        data = json.loads(json_string) # Преобразуем строку JSON в словарь Python
        return data
    except json.JSONDecodeError as e:
        print(f"JSONDecodeError: {e}")
        print(f"Не удалось декодировать JSON. Ответ от Gemini: {response.text}")
        return None  # Или вернуть какой-то другой объект по умолчанию
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


# Пример использования
if __name__ == "__main__":
    sample_text = """
# Видаткова накладна № 675 від 16 квітня 2025 р.

**Постачальник:** ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ "ПРЕСТИЖ ПРОДУКТ.К"
п/р UA203052990000026001015000569 у банку АТ КБ "ПРИВАТБАНК", м.Київ,
вул. Бескидська, буд. 41, м. ЛЬВІВ, 79067,, тел.: +38 (050) 428-54-89,
код за ЄДРПОУ 41098985, ІПН 410989826574

**Покупець:** ТОВ "РОЗЕТКА.УА"
01103, м.Київ, б-р Дружби Народів, дом № 8-A

**Договір:** № 2022/113457/Р-П від 23.06.2022

**Угода:** Замовлення покупця № 631 від 15 квітня 2025 р.

**Адреса доставки:** Украина, адрес склада: г. Киев, Кирилловская 82 , адресная доставка-ТОВ "РОЗЕТКА. УА" 37193071
Сокаль Олексій Васильович
контактний телефон 068-127-75-06 (номер вказывается, но он не рабочий)

**№ замовлення:** РО350346203 від 15.04.2025 р.

| №   | Артикул      | Штрихкод       | Код УКТЗЕД | Товар                                        | Кількість | Ціна без пдв | Ціна з пдв | Сума з ПДВ |
| --- | ------------ | -------------- | ---------- | -------------------------------------------- | --------- | ------------ | ---------- | ---------- |
| 1   | 790796/100516 | 8680211790796  | 2106 90 92 00 | Mesh Чай з шавлії 32 г (2 г Х 16 шт) Х 12 (09.01.2026) | 1 шт      | 58,65      | 70,38      | 70,38      |

Разом: 70,38
У тому числі ПДВ: 11,73
Кількість: 1
Вага (нетто): 0,03

Сімдесят гривень 38 копійок
У т.ч. ПДВ: Одинадцять гривень 73 копійки

Від постачальника*

Директор Валяєв Расім Сейранович
* Відповідальний за здійснення господарської операції і правильність її оформлення

Отримав(ла)

За довіреністю № від
"""

    extracted_data = extract_document_data(sample_text)

    if extracted_data:
        print(json.dumps(extracted_data, indent=2, ensure_ascii=False))

