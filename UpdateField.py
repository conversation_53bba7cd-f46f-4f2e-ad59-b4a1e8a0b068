import asyncio
import json
import os
import sys
import pandas as pd
import psycopg2
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_sync
from SavePDFPageAsImage import extract_pages_from_pdf
from prompt import PROMPT_AMOUNT_WITH_VAT

load_dotenv()
connection_params = {
    'host': os.getenv("PG_HOST_LOCAL", ""),
    'database': os.getenv("PG_DBNAME", ""),
    'user': os.getenv("PG_USER", ""),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", ""))
}


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()


            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            json_string = json_string.replace('\n', '')
            json_string = json_string.replace("'", '"')

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")  # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


def sync_execute(sql: str, params=None):
    conn = psycopg2.connect(**connection_params)
    cursor = conn.cursor()
    try:
        cursor.execute(sql, params)
        content = cursor.fetchall()
        if content is not None:
            return content
        else:
            return None

    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()


def get_not_correct_data_from_db()->pd.DataFrame:
    # извлекаем строки у которых некорректные данные
    sql = """
        SELECT full_path, page_number
        FROM t_scan_documents_raw
        WHERE id IN (
            SELECT external_id
            FROM t_scan_documents
            WHERE external_id NOT IN  (1231, 1426, 1465, 1491, 1724)
                AND NOT (doc_type = 'АКТ' AND amount_with_vat = 0)
                AND amount_with_vat = 0
        )
        ORDER BY
            full_path, 
            page_number
        ;
    """
    df = pd.read_sql(sql, connection_params)
    return df.to_dict(orient='records')


def get_record_dy_external_id(external_id):
    sql = """
    SELECT *
    FROM t_scan_documents
    WHERE external_id = %s
    """
    params = (external_id,)
    df = pd.read_sql(sql, connection_params, params=params)
    return df.to_dict(orient='records')[0]


def exists_correct_response(response:json):
    # проверяем, все ли сущности ИИ корректно извлек из текста.
    page_type = response['page_type']
    if int(response['page_type']) == 1 and response['doc_type'] == 'ТТН' and int(response['amount_with_vat']) == 0:
        pdf_or_image_file_path = response['full_path']
        extract_pages_from_pdf(pdf_or_image_file_path, page_number)


def main_update_field():
    df_raw = get_not_correct_data_from_db()
    if df_raw.empty():
        sys.exit(1)

    for row in df_raw:
        id = row['id']
        full_path = row['full_path']
        page_number = row['page_number']
        ocr_text = extract_pages_from_pdf(full_path, page_number)
        amount_with_vat = extract_data_from_text_by_mistral_sync(ocr_text, PROMPT_AMOUNT_WITH_VAT)
        exists_correct_response(amount_with_vat)


if __name__ == "__main__":
    main_update_field()
