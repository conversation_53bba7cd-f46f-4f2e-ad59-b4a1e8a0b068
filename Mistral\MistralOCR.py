# pip install mistralai - установка библиотеки MistralAI, необходима для работы скрипта
# pip install markdown-to-json
# извлекает данные из PDF-файла с помощью библиотеки MistralAI
# Загружаемые файлы документов не должны превышать размер 50 МБ и не должны быть длиннее 1000 страниц.

import base64
import os
import json
from mistralai.models import OCRResponse
import re
from mistralai import Mistral
from dotenv import load_dotenv


# --- Загрузка переменных окружения ---
load_dotenv()
MISTRAL_API_KEY = os.environ["MISTRAL_API_KEY"]


def replace_images_in_markdown(markdown_str: str, images_dict: dict) -> str:
    """
    Replace image placeholders in markdown with base64-encoded images.

    Args:
        markdown_str: Markdown text containing image placeholders
        images_dict: Dictionary mapping image IDs to base64 strings

    Returns:
        Markdown text with images replaced by base64 data
    """
    for img_name, base64_str in images_dict.items():
        # markdown_str = markdown_str.replace(
        #     f"![{img_name}]({img_name})", f"![{img_name}]({base64_str})"
        # )
        markdown_str = markdown_str.replace(f"![{img_name}]({img_name})", "")
    return markdown_str


def get_combined_markdown(ocr_response: OCRResponse) -> str:
    """
    Combine OCR text and images into a single markdown document.

    Args:
        ocr_response: Response from OCR processing containing text and images

    Returns:
        Combined markdown string with embedded images
    """
    markdowns: list[str] = []
    # Extract images from page
    for page in ocr_response.pages:
        image_data = {}
        for img in page.images:
            image_data[img.id] = img.image_base64
        # Replace image placeholders with actual images
        markdowns.append(replace_images_in_markdown(page.markdown, image_data))

    result = "\n\n".join(markdowns)
    return result


def encode_pdf(pdf_or_image_path: str):
    """Encode the pdf to base64."""
    try:
        with open(pdf_or_image_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_or_image_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def get_client_and_encode_pdf(pdf_or_image_path: str):
    # передать PDF-файл в кодировке Base64:
    base64_pdf = encode_pdf(pdf_or_image_path)
    if base64_pdf is None:
        raise ValueError("Ошибка при кодировании PDF-файла в Base64")

    api_key = os.environ["MISTRAL_API_KEY"]
    client = Mistral(api_key=api_key)

    return client, base64_pdf


def get_file_extension(file_path: str) -> str:
    """
    Get the file extension from the file path.

    Args:
        file_path: Path to the file

    Returns:
        File extension (e.g., 'pdf', 'png')
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_document_type(file_path: str, base64) -> dict:
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        document = {
            "type": 'document_url',
            'document_url': f"data:application/pdf;base64,{base64}",
        }
        return document
    elif ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        document = {
            "type": "image_url",
            "image_url": f"data:image/jpeg;base64,{base64}"
        }
        return document
    else:
        raise ValueError(f"Unsupported file type: {ext}")


def get_data_from_pdf(pdf_or_image_path: str):
    client, base64_pdf = get_client_and_encode_pdf(pdf_or_image_path)
    document_type = get_document_type(pdf_or_image_path, base64_pdf)
    if document_type is None:
        print(f"Не удалось определить тип документа: {pdf_or_image_path}")
        return None
    try:
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document=document_type,
        )
        return ocr_response
    except Exception as e:
        print(f"Ошибка при обработке PDF-файла: {pdf_or_image_path}")
        print(f"Ошибка: {e}")
        return None

def extract_ocr_data_by_mistral(pdf_or_image_path: str):
    pdf_response = get_data_from_pdf(pdf_or_image_path)
    if pdf_response is None:
        print(f"Не удалось получить данные из PDF-файла: {pdf_or_image_path}")
        return None

    all_text = []
    ocr_text = pdf_response
    # извлекаем текст с каждой страницы если pdf или изображение
    for i, page in enumerate(pdf_response.pages):
        image_data = {}
        for img in page.images:
            image_data[img.id] = img.image_base64

        ocr_text = replace_images_in_markdown(page.markdown, image_data)
        # all_text.append({'page_number': i + 1, 'text': ocr_text})
    return ocr_text  # all_text


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # Размер файла не должен превышать 50 МБ и не должен быть длиннее 1000 страниц.
    # Пример пути к PDF-файлу или изображению
    pdf_or_image_path = r"d:\Scan\TestVN2str.pdf"  # r"D:\Prestige\Python\ScanDocument\temp_image\TestVN2str_page_2.png"
    print(f"Обрабатываем файл: {pdf_or_image_path}")

    text = extract_ocr_data_by_mistral(pdf_or_image_path)
    print(text)
