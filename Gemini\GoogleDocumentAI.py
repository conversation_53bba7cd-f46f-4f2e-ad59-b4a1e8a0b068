# извлечение данных из документов с помощью Google Document AI
# Установите Google Cloud SDK !!!
# в cmd выполнить gcloud auth application-default login, выбрать project_id и выставить 622152118679
# в cmd выполнить gcloud config set project 622152118679
# или выбрать другой проект из https://console.cloud.google.com/projectselector2/home/<USER>
# pip install --upgrade google-cloud-documentai
from typing import Optional
from google.api_core.client_options import ClientOptions
from google.cloud import documentai  # type: ignore
from google.auth import default
from dotenv import load_dotenv
import os
import sys
import asyncio
from typing import Optional, Union
from google.cloud import documentai_v1beta3 as documentai
from google.api_core.client_options import ClientOptions


load_dotenv()

# Проверяем аутентификацию
try:
    credentials, project = default()
    # print(f"Успешная аутентификация. Используется проект по умолчанию: {project}")
except Exception as e:
    print(f"Ошибка аутентификации: {str(e)}")
    # print("Пожалуйста, выполните команду 'gcloud auth application-default login'")
    sys.exit(1)



# TODO(developer): Uncomment these variables before running the sample.
project_id = os.getenv("GOOGLE_PROJECT_NUMBER")
location = os.getenv("GOOGLE_PROJECT_LOCATION")
processor_id = os.getenv("GOOGLE_OCR_PROCESSOR_ID")
processor_name = os.getenv("GOOGLE_OCR_PROCESSOR_NAME")
# file_path = "/path/to/local/pdf"
# mime_type = "application/pdf" # Refer to https://cloud.google.com/document-ai/docs/file-types for supported file types
# field_mask = "text,entities,pages.pageNumber"  # Optional. The fields to return in the Document object.
# processor_version_id = "YOUR_PROCESSOR_VERSION_ID" # Optional. Processor version to use


def google_ocr_documentai(
    file_path: str,
    page_number: int = [1],  # Обработка только указанных страниц
    mime_type: Optional[str] = "image/png",  # image/png
    field_mask: Optional[str] = None,
    processor_version_id: Optional[str] = None,
) -> Optional[str]:

    # You must set the `api_endpoint` if you use a location other than "us".
    opts = ClientOptions(api_endpoint=f"{location}-documentai.googleapis.com")
    # print(f"API endpoint: {location}-documentai.googleapis.com")

    try:
        client = documentai.DocumentProcessorServiceClient(client_options=opts)
        # print("Клиент DocumentAI успешно создан")

        if processor_version_id:
            name = client.processor_version_path(
                project_id, location, processor_id, processor_version_id
            )
        else:
            name = client.processor_path(project_id, location, processor_id)
        # print(f"Полный путь к процессору: {name}")

        # Check if file exists
        if not os.path.exists(file_path):
            print(f"Ошибка: Файл не найден: {file_path}")
            return

        # Read the file into memory
        with open(file_path, "rb") as image:
            image_content = image.read()
        # print(f"Файл успешно прочитан, размер: {len(image_content)} байт")

        # Load binary data
        raw_document = documentai.RawDocument(content=image_content, mime_type=mime_type)

        # For more information: https://cloud.google.com/document-ai/docs/reference/rest/v1/ProcessOptions
        # Optional: Additional configurations for processing.
        process_options = documentai.ProcessOptions(
            # Process only specific pages
            individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                pages=page_number  # Обработка только первой страницы
            )
        )

        request = documentai.ProcessRequest(
            name=name,  # Полный путь к процессору
            raw_document=raw_document,
            field_mask=field_mask,
            process_options=process_options,  # Опционально: дополнительные параметры обработки
        )

        result = client.process_document(request=request)
        document = result.document
        return document.text
    except Exception as e:
        print(f"Произошла ошибка при обработке документа: {str(e)}")
        pass
        return None



async def google_ocr_documentai_async(
        file_path: str,
        page_number: Union[int, list[int]] = [1],
        mime_type: Optional[str] = "image/png",
        field_mask: Optional[str] = None,
        processor_version_id: Optional[str] = None,
) -> Optional[str]:
    def sync_process():
        try:
            opts = ClientOptions(api_endpoint=f"{location}-documentai.googleapis.com")
            client = documentai.DocumentProcessorServiceClient(client_options=opts)

            name = (
                client.processor_version_path(project_id, location, processor_id, processor_version_id)
                if processor_version_id
                else client.processor_path(project_id, location, processor_id)
            )

            if not os.path.exists(file_path):
                print(f"Ошибка: Файл не найден: {file_path}")
                return None

            with open(file_path, "rb") as image:
                image_content = image.read()

            raw_document = documentai.RawDocument(content=image_content, mime_type=mime_type)

            process_options = documentai.ProcessOptions(
                individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                    pages=page_number if isinstance(page_number, list) else [page_number]
                )
            )

            request = documentai.ProcessRequest(
                name=name,
                raw_document=raw_document,
                field_mask=field_mask,
                process_options=process_options,
            )

            result = client.process_document(request=request)
            return result.document.text
        except Exception as e:
            print(f"Произошла ошибка при обработке документа: {str(e)}")
            return None

    return await asyncio.to_thread(sync_process)


if __name__ == "__main__":
    # result = google_ocr_documentai(
    #     file_path= r"c:\Scan\All\TestVN2str.pdf",
    #     page_number= [1,2],
    #     mime_type= "application/pdf",  # image/png
    # )

    image_path = r"C:\Rasim\Python\ScanDocument\temp_image\2025-05-01_120132_page_3.png"
    result = google_ocr_documentai(image_path,[1],"image/png")
    print(result)