# Извлечение текста из PDF файлов целиком с помощью Gemini API
import google.generativeai as genai
import os
import re
import sys
from pathlib import Path
from dotenv import load_dotenv
import time
import logging
from GeminiTokenCount import get_token_count
from typing import Dict, Any, Union, List
from prompt import PROMPT_EXAMPLE, PROMPT_OCR
import json
from time import sleep


# --- Настройка логирования ---
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# --- Загрузка переменных окружения ---
load_dotenv()

# --- Очистка консоли ---
os.system('cls' if os.name == 'nt' else 'clear')

# --- Настройки API и Путей ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY_PRESTIGE')
MODEL_NAME = os.getenv("GEMINI_MODEL_2F")
PDF_INPUT_DIRECTORY = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\Parsed"

if not GEMINI_API_KEY:
    raise ValueError("Необходимо установить переменную окружения GEMINI_API_KEY.")

if not MODEL_NAME:
    raise ValueError("Необходимо установить переменную окружения GEMINI_MODEL_2F.")




def clear_text(json_string) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


async def add_thinking_content_to_dict(model: str, response: str) -> Dict[str, Any]:
    # Ответ. Что нам и надо. Проверяем чтобы был корректный JSON
    content = clear_text(response.content if response.content else "")

    # deepseek-reasoner есть reasoning_content. Обоснование ответа.
    # Если ответ дает не точный, можно отследить причину и изменить промт
    if model != "deepseek-reasoner": # Имя модели из вашего оригинального кода
        return content

    # Получаем обоснование ответа.
    thinking_content = getattr(response, 'reasoning_content', "")  # reasoning_content - чем обосновывает свой ответ

    # Проверка, что 'doc' есть в data и он является списком
    if not content or 'doc' not in content or not isinstance(content.get('doc', []), list):
        return content

    # Переводим обоснование на русский. Используем Gemini API
    # КЛЮЧЕВОЕ ИЗМЕНЕНИЕ: делаем вызов translate_by_gemini асинхронным
    # Это потребует, чтобы translate_by_gemini была async def или обернута
    # thinking_content = await translate_by_gemini(thinking_content)

    # Добавляем thinking_content в каждый элемент списка
    for item in content['doc']:
        if isinstance(item, dict):
            item['thinking_content'] = thinking_content

    return content


# --- Утилиты ---
def delete_file_with_retry(file_name, max_retries=5, base_delay=2):
    """Удаляет файл с сервера с экспоненциальной задержкой"""
    attempt = 0
    while attempt < max_retries:
        try:
            genai.delete_file(file_name)
            logger.debug(f"Файл {file_name} успешно удален с сервера")
            return True
        except Exception as e:
            attempt += 1
            delay = base_delay * (2 ** attempt) + (0.1 * attempt)
            logger.warning(f"Ошибка удаления файла {file_name} (попытка {attempt}/{max_retries}): {e}")
            if attempt < max_retries:
                time.sleep(delay)
    
    logger.error(f"Не удалось удалить файл {file_name} после {max_retries} попыток")
    return False


def find_pdf_files(directory):
    """Рекурсивно ищет PDF файлы"""
    pdf_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.pdf'):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(root, directory)
                pdf_files.append((full_path, file, rel_path))
    return pdf_files


def validate_response_quality(response_text: str, min_meaningful_chars: int = 50) -> tuple[bool, str]:
    """
    Проверяет качество ответа от Gemini.
    
    Args:
        response_text: Текст ответа для проверки
        min_meaningful_chars: Минимальное количество осмысленных символов
        
    Returns:
        Tuple (is_valid, reason) - валиден ли ответ и причина отклонения
    """
    if not response_text or not isinstance(response_text, str):
        return False, "Пустой или невалидный ответ"
    
    # Убираем markdown разметку для анализа
    clean_text = re.sub(r'```markdown\s*', '', response_text)
    clean_text = re.sub(r'```\s*$', '', clean_text)
    clean_text = clean_text.strip()
    
    # Проверка на специальные маркеры
    if clean_text == "EMPTY_PAGE":
        return True, "Пустая страница"
    
    # Проверка на слишком короткий ответ
    if len(clean_text) < min_meaningful_chars:
        return False, f"Слишком короткий ответ ({len(clean_text)} символов)"
    
    # Проверка на типичные "пустые" ответы
    meaningless_patterns = [
        r'^[\s]+$',  # Только пробелы
        r'^[-\s]*$',  # Только тире и пробелы
        r'^[.\s]*$',  # Только точки и пробелы
        r'^[_\s]*$',  # Только подчеркивания и пробелы
        r'^[*\s]*$',  # Только звездочки и пробелы
        r'^[#\s]*$',  # Только решетки и пробелы
        r'^[=\s]*$',  # Только знаки равенства и пробелы
        r'^[+\s]*$',  # Только плюсы и пробелы
        r'^[|\\s]*$', # Только вертикальные линии и пробелы
        r'^[|-]*$', # Только вертикальные линии и пробелы
    ]
    
    for pattern in meaningless_patterns:
        if re.match(pattern, clean_text):
            return False, "Ответ содержит только символы-заполнители"
    
    # Проверка на повторяющиеся символы (более 50% одинаковых символов подряд)
    total_chars = len(clean_text)
    if total_chars > 0:
        # Ищем повторяющиеся последовательности
        repeated_chars = re.findall(r'(.)\1{4,}', clean_text)  # 5+ одинаковых символов подряд
        repeated_count = sum(len(match) for match in repeated_chars)
        
        if repeated_count > total_chars * 0.3:  # Более 30% повторяющихся символов
            return False, "Слишком много повторяющихся символов"
    
    # Проверка на наличие осмысленных украинских слов
    ukrainian_letters = re.findall(r'[а-яїєіґА-ЯЇЄІҐ]', clean_text)
    if len(ukrainian_letters) < min_meaningful_chars * 0.3:  # Минимум 30% украинских букв
        return False, "Недостаточно украинского текста"
    
    # Проверка на наличие осмысленных слов (минимум 3 буквы)
    meaningful_words = re.findall(r'\b[а-яїєіґА-ЯЇЄІҐ]{3,}\b', clean_text)
    if len(meaningful_words) < 3:
        return False, "Недостаточно осмысленных слов"
    
    return True, "Ответ прошел валидацию"


def extract_text_from_pdf_with_gemini(pdf_path: str, prompt: str = PROMPT_OCR) -> str:
    """Извлекает текст из PDF с помощью Gemini API с проверкой качества ответа"""

    max_attempts = 5  # Увеличиваем количество попыток
    base_delay = 2 # Базовая задержка между попытками в секундах
    
    for attempt in range(max_attempts):
        uploaded_file = None
        try:
            logger.info(f"Попытка {attempt + 1}/{max_attempts} обработки файла {os.path.basename(pdf_path)}")
            
            # Загрузка файла
            uploaded_file = genai.upload_file(
                path=pdf_path,
                display_name=os.path.basename(pdf_path)
            )
            
            # Ждем обработки файла
            time.sleep(2)
            
            # Формирование запроса
            prompt_file = [prompt, uploaded_file]
            generation_config = {
                "temperature": 0.1,
                "top_p": 0.95,
                "top_k": 30,
                "max_output_tokens": 65000 # Увеличиваем лимит токенов
            }
            
            # Отправка запроса
            model = genai.GenerativeModel(MODEL_NAME)
            response = model.generate_content(prompt_file, generation_config=generation_config)
            
            # Обязательно подсчитываем токены
            try:
                get_token_count(response)
            except Exception as token_error:
                logger.warning(f"Ошибка подсчета токенов: {token_error}")
            
            # Обработка ответа
            extracted_text = response.text if response.text else ""
            extracted_text = extracted_text.strip()

            if not extracted_text:
                logger.warning(f"Пустой ответ для файла {os.path.basename(pdf_path)} (попытка {attempt + 1})")
                if attempt < max_attempts - 1:
                    delay = base_delay * (2 ** attempt)
                    time.sleep(delay)
                    continue
                return None
            
            # Проверяем качество ответа
            is_valid, reason = validate_response_quality(extracted_text)
            
            if not is_valid:
                logger.warning(f"Ответ не прошел валидацию (попытка {attempt + 1}): {reason}")
                if attempt < max_attempts - 1:
                    delay = base_delay * (2 ** attempt)
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"Все попытки исчерпаны. Последняя причина: {reason}")
                    return None
            
            logger.info(f"Ответ прошел валидацию: {reason}")
            
            # Очистка ответа от артефактов
            raw_text = extracted_text.replace(
                "```markdown\n# !!!ОБЯЗАТЕЛЬНО вернись и расставь разметки markdown.!!!\n# !!!ОЧЕНЬ ВАЖНО!!! Таблицы форматируй правильно!!!\n", "")
            cleaned_text = clear_text(raw_text)
            return cleaned_text
        
        except Exception as e:
            error_message = str(e).lower()
            
            # Проверяем на ошибки сервиса
            if any(keyword in error_message for keyword in ["503", "service is currently unavailable", "temporarily unavailable", "quota", "rate limit"]):
                delay = base_delay * (2 ** attempt) + (0.5 * attempt)
                # logger.warning(f"Ошибка сервиса ({attempt+1}/{max_attempts}) для {os.path.basename(pdf_path)}: {str(e)}")
                if attempt < max_attempts - 1:
                    # logger.info(f"Ожидание {delay:.1f} сек перед повторной попыткой...")
                    time.sleep(delay)
                continue
            else:
                logger.error(f"Критическая ошибка для {os.path.basename(pdf_path)}: {str(e)}")
                sleep(5)
                break
            
        finally:
            # Всегда пытаемся удалить файл с сервера
            if uploaded_file:
                try:
                    delete_file_with_retry(uploaded_file.name)
                except Exception as delete_error:
                    logger.error(f"Ошибка при попытке удаления файла {uploaded_file.name}: {delete_error}")
    
    logger.error(f"Не удалось обработать файл {os.path.basename(pdf_path)} после {max_attempts} попыток")
    return None


# --- Основной скрипт ---
if __name__ == "__main__":
    genai.configure(api_key=GEMINI_API_KEY)

    if not os.path.isdir(PDF_INPUT_DIRECTORY):
        logger.error(f"Директория не найдена: {PDF_INPUT_DIRECTORY}")
        sys.exit(1)

    pdf_files = find_pdf_files(PDF_INPUT_DIRECTORY)
    if not pdf_files:
        logger.info("PDF файлы не найдены")
        sys.exit(0)
    
    logger.info(f"Найдено PDF файлов: {len(pdf_files)}")
    processed_count = 0
    errors = 0

    for full_path, filename, rel_path in pdf_files:
        logger.info(f"Начало обработки: {filename}")
        
        # Создание структуры каталогов
        output_dir = Path(PDF_INPUT_DIRECTORY) / "pdf_extracted_text" / rel_path
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"{os.path.splitext(filename)[0]}.txt"
        
        # Проверяем, не обработан ли уже файл
        if output_path.exists():
            logger.info(f"Файл уже обработан, пропускаем: {filename}")
            processed_count += 1
            continue
        
        # Обработка файла
        start_time = time.time()
        text = extract_text_from_pdf_with_gemini(full_path)
        processing_time = time.time() - start_time
        
        # if text:
        #     try:
        #         with open(output_path, "w", encoding="utf-8") as f:
        #             f.write(text)
        #         processed_count += 1
        #         logger.info(f"✓ Успешно обработан за {processing_time:.1f}с: {filename}")
        #     except Exception as e:
        #         logger.error(f"Ошибка записи {output_path}: {str(e)}")
        #         errors += 1
        # else:
        #     logger.error(f"✗ Не удалось извлечь текст: {filename}")
        #     errors += 1

    logger.info(f"Обработка завершена. Успешно: {processed_count}, Ошибки: {errors}, Всего: {len(pdf_files)}")