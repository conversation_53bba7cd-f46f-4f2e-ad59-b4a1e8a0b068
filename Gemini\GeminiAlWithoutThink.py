# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import json
import os
from google import genai
from google.genai import types
from prompt import GEMINI_AI_PROMPT, PROMPT_OCR, PROMPT_EXAMPLE
from typing import Dict, Any, Union
from dotenv import load_dotenv
import fitz  # PyMuPDF
# Загружаем переменные окружения из .env файла
load_dotenv()


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")  # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
        if not os.path.exists(pdf_path):
            return None
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def generate(pdf_path: str):
    """Generate a response from the model."""
    doc = fitz.open(pdf_path)
    total_pages = doc.page_count

    pdf_decoded = encode_pdf(pdf_path)
    if pdf_decoded is None:
        return None
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )
    # model = "gemini-2.5-pro-preview-05-06"
    model = os.getenv("GEMINI_MODEL_2F")
    extract_data = """верни СТРОГО в формате валидного JSON.:
        [{
        "file_name": string, // Имя файла. Тебе дал
        "page_num": number, // Номер страницы
        "doc_number": number, // Номер документа (если есть)
        "doc_date": date, // Дата документа (если есть)
        "doc_type": string, // Тип документа (если есть). Коротко
        "buyer_name": string, // Имя клиента (если есть). Коротко. Без кавычек и статуса. Престиж - игнорируй
        "buyer_code": number, // Код клиента (если есть). Строго 8 цифр
        "amount_with_vat": number, // Сумма с НДС (если есть). В некоторых документах может быть прописью
        "rows":[number], // Номера строк. Колонка №. Если нет строк, то пустой массив. по возрастанию номера строки
        }]
    """
    prompt = "PROMPT: " + extract_data + '\n\n' + (f'** Количество страниц: {total_pages}. '
                                                   f'Извлеки с каждой страницы ТОЛЬКО те данные, которые указал (СТРОГО!!!) в указанном формате. **\n\n') + PROMPT_OCR  # PROMPT_EXAMPLE
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text="file_name: " + os.path.basename(pdf_path) + prompt),
            ],
        ),
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text="""INSERT_INPUT_HERE"""),
            ],
        ),
    ]
    tools = [
        types.Tool(google_search=types.GoogleSearch()),
    ]
    generate_content_config = types.GenerateContentConfig(
        temperature=0.1,
        tools=tools,
        response_mime_type="text/plain",
    )

    response_text = ""
    for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
    ):
        response_text += chunk.text

    cleared_response_json = clear_text(response_text)
    return cleared_response_json


def save_result_to_json(result: json, output_path: str) -> None:
    """Сохраняет результат анализа в JSON файл.

    Args:
        result: Словарь с результатами анализа
        output_path: Путь к файлу для сохранения
    """
    with open(output_path, "w", encoding="utf-8") as json_file:
        json.dump(result, json_file, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    from datetime import datetime
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
    pdf_path = r"d:\Scan\20250430_Merge 001-100.pdf"
    result = generate(pdf_path)
    current_date_time = datetime.now().strftime("%Y%m%d %H%M%S")
    base_name = str(pdf_path).replace(".pdf", f" {current_date_time}.json")
    save_result_to_json(result,  base_name)
    print(result)