# To run this code you need to install the following dependencies:
# pip install google-genai
# pip install --upgrade google-generativeai
# программа создана с помощью https://aistudio.google.com/prompts/1VvAiD-oYWC3g94h0wTOUMvzU7BpKrgn_

import base64
import os
from google import genai
from google.genai import types
from typing import Dict, Any, Union
import json
from dotenv import load_dotenv


# Загружаем переменные окружения из .env файла
load_dotenv()


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]
                
            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def generate(pdf_path: str):
    """Generate a response from the model."""
    
    pdf_decoded = encode_pdf(pdf_path)
    if pdf_decoded is None:
        return None
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-pro-preview-05-06"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text= "file_name: " + os.path.basename(pdf_path) + """
Текст на украинском языке. Твоя задача извлечь:
1) тип документа. \\\\\\\"Товарно-Транспортная\\\\\\\" (ТТН), \\\\\\\"Видаткова накладна\\\\\\\" (ВН), Акт, \\\\\\\"Повернення посточальнику\\\\\\\" (ПП), Не смог определить - \\\\\\\"Другой\\\\\\\".
2) Наименование клиента/покупателя. Без Правового статуса. Имя клиента полностью, большими буквами. Данные продавца игнорируй.
3) код покупателя // для уникальности клиента.
4) страницы, которые относятся к данному документу
5) определи где первая страница, где середина и где последняя страница
6) страницы у тебя повторяться не должны. Одна страница относится только к одному документу.
7) номера документов, на которые ссылается ТТН.
8) Если у ТТН нет даты и она ссылается на несколько ВН с разными датами, бери самую позднюю или если все даты совпадают бери любую.
9) Укажи сумму с НДС. Для ВН она на последней странице(если много страниц). Для ТТН сумма с НДС указана на первой странице. Пишется прописью
10) Если код клиента одинаковый, наименования верни одинаковыми - верхний регистр.
11) колонка № номера строк (если есть), указанные в порядке возрастания
12) сложи все суммы с НДС у ВН, на которые ссылается ТТН и сравни с суммой указанной в ТТН. 
13) Два одинаковых ТТН могут иметь одинаковые номера, наименования и код покупателя, поставщика, но ссылаться на разные ВН. Это разные ТТН. Вместе не объединяй их.

Для этого ты должен очень углубленно вникнуть в каждую деталь. Найти по каким ключевым данным определить какую страницу объединять с каким документом.
верни в формате валидного json:
{doc: [
file_name: string, // я тебе его не предоставил. Не меняй.
doc_type: string, // тип документа: \"ТТН\", \"ВН\", \"ПП\", \"Акт\", \"Другой\". Коротко
doc_date: date | null, // дата в формате dd.mm.yyyy
doc_number: numeric, // число, без букв
buyer_name string, // имя клиента, Иванов И. Эпицентр К. Правовой статус игнорируй. Коротко, без кавычек.
buyer_code numeric, // 8 знаков, число
page_numbers: [numeric], //  [8,4,7,3] номера страниц сортируй первая страница, середина, последняя страница. Не по возрастанию номеров страниц - а логически. Если середина документа состоит из нескольких страниц, тогда при сортировке страниц воспользуйся номерами строк - rows_list.
invoice_numbers: [numeric], // номера документов ВН на которые ссылается ТТН. По возрастанию
amount_with_vat:  numeric, // сумма с НДС число,
rows_list: [numeric], // номера строк по возрастанию [1,2,3]
diff_amount_with_vat:numeric  // сумма расхождения. сумма ТТН - (сумма ВН1 + сумма ВН2...)
]
}
*****
Ты должен перепроверить и дать себе ответ. Например:
1) как определил что страницы 10 и 9 относятся к одному и тому же документу.
2) как определил что сначала надо ставить страницу 10, потом 9.
3) как смог определить дату 21.09.2024 у документа. В документе даты не было.
4) сложил все суммы по ВН и сравнил с суммой, на которые ссылается ТТН - результат не сошелся. перепроверил ли страницы?
Ты включил абсолютно все свои профессиональные аналитические возможности и перепроверил свои данные углубляясь в каждую деталь и используя различные подходы, методы и переспрашивая себя а вдруг неправильно, ты нашел разные варианты как обосновать свой результат. Ты смог доказать, что ты - профессионал!"""),
            ],
        ),
        types.Content(
            role="model",
            parts=[
                types.Part.from_text(text="""**Clarifying Extraction Strategy**

I've been focusing on developing a robust strategy for extracting key data from these Ukrainian documents. Currently, I'm prioritizing accuracy and efficiency, with a particular emphasis on handling variations in document formatting. The initial stages of parsing and identifying the necessary elements seem solid, and I'm now fine-tuning the approach to account for potential inconsistencies.


**Refining Data Parsing**

I'm currently focused on refining the data parsing logic to handle complex document structures. I've identified key fields and am working on robust methods for extracting them. Initial tests show promise. Parsing of \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\" (TTN) and \"Видаткова накладна\" (ВН) are progressing well, with accurate identification of most data points. I'm now testing edge cases with various formatting and document layout.


**Defining Document Grouping**

I'm now focusing on a more granular approach to document grouping.  I've shifted from initial identification to establishing the specific relationships between TTN and ВН documents based on the extracted numbers, particularly the 'Видаткова накладна' references.  This meticulous linking of forward and reverse sides is crucial for accurate data association.


**Establishing Document Relationships**

I'm now focusing on establishing and organizing the relationships between the different documents (TTN and ВН) and pages to finalize the structure for the JSON output. Initial analysis confirms each TTN is linked to a ВН. The primary goal is to determine the correct page order for both types of documents, matching front and back, and then link them as TTN and associated ВН. My progress includes successfully matching the \"Документи з вантажем\" and the corresponding \"Видаткова накладна\" numbers to properly group and pair them.


**Organizing Document Structure**

I'm now focusing on a more granular approach to document grouping.  I've shifted from initial identification to establishing the specific relationships between TTN and ВН documents. This includes the precise ordering of the pages, forward and reverse, as well as the identification of relationships between each TTN and its corresponding ВН. I'm building a robust system to accurately pair these documents and correctly construct the data structure, including handling any standalone invoices. The page-level analysis is proving pivotal in clarifying the structure.


**Defining Data Structures**

I'm now focusing on defining the structures for the JSON output. I've successfully identified and grouped the documents and pages, establishing their relationships. This includes accurately pairing TTNs with their corresponding ВНs, and separating standalone ВНs. I'm now structuring the output to reflect these identified document groups. Key information like document type, date, number, and linked invoice numbers will be included.


**Constructing the JSON Output**

I'm now focusing on the final JSON output structure, taking into account the identified document groupings and relationships.  I've finalized the data extraction process and am now compiling the key data elements for each document. The goal is to provide a clear and organized data representation, including invoice links and financial values, in a consistent format. My next step will be testing this structure with the sample documents.


**Constructing JSON Output**

I've successfully identified and grouped documents, extracting key data points and their corresponding pages. For each TTN, I'm carefully compiling linked invoice numbers, document dates, and buyer details. The JSON output format now precisely mirrors the document structure, ensuring each TTN and ВН are correctly associated. I'm focusing on ensuring accuracy in financial calculations and invoice pairings, preparing for a comprehensive validation phase.


**Implementing JSON Structure**

I've been working on structuring the JSON output based on the identified document groups. This includes defining key fields for TTNs and ВНs, establishing relationships, and organizing the data hierarchy. I'm focusing on ensuring data integrity and consistency for accurate and efficient processing. Now, I'm integrating the page order and relationship data into the JSON, ensuring the output reflects the document structure and associations accurately.


**Defining Data Output**

I'm now implementing the JSON structure, having established clear data groupings and document relationships. I'm focusing on incorporating extracted data points and calculations like `diff_amount_with_vat`. The current phase is about assembling the elements. I'm focusing on ensuring accuracy in the calculation of `diff_amount_with_vat`, considering that only a subset of associated invoices have been provided.


**Implementing Data Integrity**

I'm now implementing the necessary data structures for the final JSON output.  I've ensured the proper linkage between TTNs and ВНs, and the accurate representation of all necessary data. My focus is on data consistency and integrity, including checking and addressing edge cases such as missing information. I'm now testing the output for completeness and accuracy, focusing specifically on verifying the `diff_amount_with_vat`.


**Finalizing JSON Structure**

I'm now integrating the extracted data into the final JSON output.  I've ensured proper page and document associations, and have completed the data extraction process. The calculation of `diff_amount_with_vat` is complete, and the focus is now on ensuring accuracy in data mapping. The JSON output will be validated against the provided documents.


**Defining and Calculating Amounts**

I'm now focusing on calculating the difference in amounts with VAT, `diff_amount_with_vat`. My goal is to accurately determine the amount difference between the TTNs and their corresponding linked ВНs. This involves referencing the linked invoices, applying any necessary logic based on what's available. The final structure of the JSON output is being finalized.


**Calculating Invoice Differentials**

I've been working on calculating the `diff_amount_with_vat` precisely. The logic is now implemented and I'm ensuring accuracy in matching invoices. I'm carefully double-checking the calculations to account for missing ВНs and correct document pairings.


**Implementing Financial Logic**

I've been working on the financial calculations, including accurately determining the `diff_amount_with_vat`. I'm now testing and validating these calculations, particularly ensuring precise pairings of ТТН and ВН documents. This final step is crucial for guaranteeing data integrity.


"""),
                types.Part.from_text(text="""```json
{
  \"doc\": [
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ТТН\",
      \"doc_date\": \"21.09.2024\",
      \"doc_number\": 4476028,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        2,
        1
      ],
      \"invoice_numbers\": [
        10890,
        10891,
        10892,
        10893,
        10894,
        10895,
        10896,
        10897,
        10898,
        10899,
        10900,
        10901,
        10902,
        10903,
        10904,
        10905,
        10906,
        10907,
        10908
      ],
      \"amount_with_vat\": 27804.24,
      \"rows_list\": [
        1
      ],
      \"diff_amount_with_vat\": null
    },
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ТТН\",
      \"doc_date\": null,
      \"doc_number\": 10742009,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        3,
        5
      ],
      \"invoice_numbers\": [
        10377
      ],
      \"amount_with_vat\": 3974.40,
      \"rows_list\": [
        2
      ],
      \"diff_amount_with_vat\": null
    },
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ВН\",
      \"doc_date\": \"29.08.2024\",
      \"doc_number\": 10378,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        4
      ],
      \"invoice_numbers\": [],
      \"amount_with_vat\": 3321.36,
      \"rows_list\": [
        1,
        2,
        3
      ],
      \"diff_amount_with_vat\": null
    },
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ВН\",
      \"doc_date\": \"21.09.2024\",
      \"doc_number\": 10898,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        8
      ],
      \"invoice_numbers\": [],
      \"amount_with_vat\": 341.28,
      \"rows_list\": [
        1
      ],
      \"diff_amount_with_vat\": null
    },
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ТТН\",
      \"doc_date\": \"21.09.2024\",
      \"doc_number\": 10802768,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        10,
        9
      ],
      \"invoice_numbers\": [
        10898
      ],
      \"amount_with_vat\": 341.28,
      \"rows_list\": [
        1
      ],
      \"diff_amount_with_vat\": 0.00
    },
    {
      \"file_name\": \"ScanForTreaningNew.pdf\",
      \"doc_type\": \"ТТН\",
      \"doc_date\": null,
      \"doc_number\": 10733683,
      \"buyer_name\": \"ЕПІЦЕНТР К\",
      \"buyer_code\": ********,
      \"page_numbers\": [
        11,
        6
      ],
      \"invoice_numbers\": [
        10278
      ],
      \"amount_with_vat\": 684.00,
      \"rows_list\": [
        1
      ],
      \"diff_amount_with_vat\": null
    }
  ]
}
```

**Самопроверка и Обоснование:**

1.  **Как определил что страницы 10 и 9 относятся к одному и тому же документу (ТТН №10802768)?**
    *   На странице 10 указано \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА № **10802768**\".
    *   На странице 10 в разделе \"Супровідні документи на вантаж\" указано \"Видаткова накладна № **10898**\".
    *   На странице 9 вверху указано \"Продовження до додатка 7 Зворотний бік\".
    *   На странице 9 в таблице \"ВІДОМОСТІ ПРО ВАНТАЖ\" в колонке \"Документи з вантажем\" указан номер **10898**.
    *   Совпадение номера ссылочной ВН (10898) и тот факт, что страница 9 является \"Зворотний бік\", однозначно связывают страницу 10 (лицевая сторона ТТН №10802768) со страницей 9 (оборотная сторона этой же ТТН).

2.  **Как определил что сначала надо ставить страницу 10, потом 9?**
    *   Страница 10 содержит заголовок \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\", её номер и дату (или место для даты), информацию об автомобиле, перевозчике, отправителе, получателе. Это стандартная структура лицевой (первой) страницы ТТН.
    *   Страница 9 явно помечена как \"Зворотний бік\" и содержит \"Продовження до додатка 7\", что является табличной частью, детализирующей груз, и обычно располагается на обороте или последующих страницах ТТН.
    *   Логически, лицевая сторона документа всегда идет первой.

3.  **Как смог определить дату 21.09.2024 у документа ТТН №10802768 (страницы 10, 9)? В документе (на стр. 10) даты не было.**
    *   На странице 10 (лицевая сторона ТТН №10802768) поле \"від \\_\\_\\_ \\_\\_\\_ р.\" (дата ТТН) не заполнено.
    *   Согласно правилу 8: \"Если у ТТН нет даты и она ссылается на несколько ВН с разными датами, бери самую позднюю или если все даты совпадают бери любую.\"
    *   ТТН №10802768 ссылается на \"Видаткова накладна № **10898**\" (указано на стр. 10).
    *   Документ \"Видаткова накладна № 10898\" (страница 8) имеет дату \"від **21 вересня 2024 р.**\".
    *   Поскольку ТТН ссылается только на одну ВН (№10898), дата этой ВН (21.09.2024) была взята как дата ТТН.

4.  **Сложил все суммы по ВН и сравнил с суммой, на которые ссылается ТТН - результат не сошелся/сошелся. Перепроверил ли страницы?**

    *   **Для ТТН №10802768 (стр. 10, 9):**
        *   Сумма с НДС в ТТН (стр. 10): \"Триста сорок один гривень 28 копійок\" = **341.28 грн**.
        *   ТТН ссылается на ВН №10898.
        *   Сумма с НДС в ВН №10898 (стр. 8, \"Усього з ПДВ:\"): **341.28 грн**.
        *   Расхождение: 341.28 (ТТН) - 341.28 (ВН) = **0.00 грн**. Результат сошелся.

    *   **Для ТТН №4476028 (стр. 2, 1):**
        *   Сумма с НДС в ТТН (стр. 2): \"Двадцать сім тисяч вісімсот чотири гривні 24 копійки\" = **27804.24 грн**.
        *   ТТН ссылается на ВН № 10890,10891,10892,10893,10894,10895,10896,10897,10898,10899,10900,10901,10902,10903,10904,10905,10906,10907,10908.
        *   Из этого списка в предоставленном наборе документов есть только ВН №10898 (стр. 8) с суммой 341.28 грн.
        *   Поскольку не все ВН, на которые ссылается ТТН, присутствуют в наборе, корректно рассчитать `diff_amount_with_vat` невозможно. Поэтому для этой ТТН `diff_amount_with_vat` установлено в `null`.

    *   **Для ТТН №10742009 (стр. 3, 5):**
        *   Сумма с НДС в ТТН (стр. 3): \"Три тисячі дев'ятсот сімдесят чотири гривні 40 копійок\" = **3974.40 грн**.
        *   ТТН ссылается на ВН №10377.
        *   ВН №10377 отсутствует в предоставленном наборе документов.
        *   `diff_amount_with_vat` установлено в `null`.

    *   **Для ТТН №10733683 (стр. 11, 6):**
        *   Сумма с НДС в ТТН (стр. 11): \"Шістьсот вісімдесят чотири гривні 00 копійок\" = **684.00 грн**.
        *   ТТН ссылается на ВН №10278.
        *   ВН №10278 отсутствует в предоставленном наборе документов.
        *   `diff_amount_with_vat` установлено в `null`.

Все проверки подтверждают корректность извлеченных данных и логики их обработки согласно поставленным задачам. Я использовал ключевые поля (номера документов, ссылки на связанные документы, фразы \"Зворотний бік\", \"Продовження до додатка\"), чтобы правильно сгруппировать страницы и извлечь информацию. Даты для ТТН без явной даты определялись по датам связанных ВН, если те были доступны. Расчет расхождений производился только при наличии всех ВН, на которые ссылается ТТН."""),
            ],
        ),
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text="""INSERT_INPUT_HERE"""),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        temperature=0.2,
        response_mime_type="text/plain",
    )

    response_text = ""
    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        response_text += chunk.text 
        
    cleared_response = clear_text(response_text)
    return cleared_response


def save_result_to_json(result:str, output_path: str) -> None:
    """Сохраняет результат анализа в JSON файл.

    Args:
        result: Словарь с результатами анализа
        output_path: Путь к файлу для сохранения
    """
    with open(output_path, "w", encoding="utf-8") as json_file:
        json.dump(result, json_file, ensure_ascii=False, indent=2)


def create_filename_from_json(json_data: Dict[str, Any]) -> str:
    buyer_name = json_data.get("buyer_name", "unknown").replace(" ","_").upper()
    buyer_code = json_data.get("buyer_code", "unknown")
    doc_type = json_data.get("doc_type", "unknown").replace(" ", "_").upper()
    doc_date = json_data.get("doc_date", "unknown")
    doc_number = json_data.get("doc_number", "unknown")
    page_numbers = json_data.get("page_numbers", [])
    if not isinstance(page_numbers, list):
        page_numbers = [page_numbers]  # Ensure page_numbers is a list
    folder_name = os.path.join(f"{buyer_code} {buyer_name}",doc_type)
    file_name = f"{buyer_code}_{buyer_name}_{doc_type}_{doc_date}_{doc_number}_pages_{'_'.join(map(str, page_numbers))}.json"
    return file_name


if __name__ == "__main__":
    from datetime import datetime
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
        
    pdf_path = r"d:\Scan\20250430_Merge 001-100.pdf"
    result = generate(pdf_path)
    current_date_time = datetime.now().strftime("%Y%m%d %H%M%S")
    base_name = str(pdf_path).replace(".pdf", f" {current_date_time}.json")
    save_result_to_json(result,  base_name)
    print(result)
