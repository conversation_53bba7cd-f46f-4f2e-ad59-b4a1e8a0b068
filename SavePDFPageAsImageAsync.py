import os
from pathlib import Path
import asyncpg
import fitz  # PyMuPDF
import asyncio
import aiofiles
from concurrent.futures import ThreadPoolExecutor
from Gemini.GoogleDocumentAIAsync import google_ocr_documentai_async
from os.path import exists
from Mistral.MistralOCRAsync import extract_ocr_data_by_mistral_async


MAX_IMAGE_SIZE_MB = 9
MIN_DPI = 50
MAX_DPI = 500
DPI_STEP = 25
MAX_PIXEL_DIMENSION = 9900  # Максимальный размер по любой стороне 10000

# Пул потоков для CPU-интенсивных операций
executor = ThreadPoolExecutor(max_workers=4)


# Получает расширение файла из пути к файлу.
def get_file_extension(file_path: str) -> str:
    return Path(file_path).suffix.lower().lstrip('.')


def calculate_max_dpi_for_dimensions(page_width, page_height, max_pixels=MAX_PIXEL_DIMENSION):
    """
    Вычисляет максимальный DPI, при котором размеры изображения не превысят лимит.
    """
    # Размеры страницы в дюймах (fitz возвращает размеры в пунктах, 1 дюйм = 72 пункта)
    width_inches = page_width / 72
    height_inches = page_height / 72

    # Максимальный DPI для каждого измерения
    max_dpi_width = max_pixels / width_inches
    max_dpi_height = max_pixels / height_inches

    # Берем минимальный из двух
    return min(max_dpi_width, max_dpi_height)


async def save_pdf_page_as_image(file_path: str, page_number: int, dpi: int = 300) -> str:
    """
    Сохраняет указанную страницу PDF как изображение PNG асинхронно,
    автоматически регулируя DPI так, чтобы размер не превышал 9 МБ 
    и размеры изображения не превышали 10k пикселей.
    """
    if get_file_extension(file_path) != 'pdf':
        raise ValueError(f"File {file_path} is not a PDF document.")
    if page_number < 1:
        raise ValueError(f"Page number {page_number} is less than 1.")

    await asyncio.to_thread(os.makedirs, "temp_image", exist_ok=True)
    base_name = f"{Path(file_path).stem}_page_{page_number}.png"
    temp_file_path = os.path.join("temp_image", base_name)

    def _process_page():
        page_number_zero = page_number - 1  # fitz использует нумерацию с 0
        doc = fitz.open(file_path)

        if page_number_zero >= len(doc):
            doc.close()
            raise ValueError(f"Страница {page_number} отсутствует в документе.")

        page = doc.load_page(page_number_zero)

        # Получаем размеры страницы
        page_rect = page.rect
        page_width = page_rect.width
        page_height = page_rect.height

        # Вычисляем максимальный DPI для соблюдения лимита размеров
        max_dpi_for_dimensions = calculate_max_dpi_for_dimensions(page_width, page_height)

        # Ограничиваем начальный DPI максимально допустимым
        current_dpi = min(dpi, max_dpi_for_dimensions, MAX_DPI)
        current_dpi = max(current_dpi, MIN_DPI)  # Не меньше минимального

        print(f"Размеры страницы: {page_width:.1f} x {page_height:.1f} пунктов")
        print(f"Максимальный DPI для размеров: {max_dpi_for_dimensions:.1f}")
        print(f"Начальный DPI: {current_dpi}")

        direction = None

        while True:
            mat = fitz.Matrix(current_dpi / 72, current_dpi / 72)

            # Проверяем размеры перед созданием изображения
            expected_width = int(page_width * current_dpi / 72)
            expected_height = int(page_height * current_dpi / 72)

            if expected_width > MAX_PIXEL_DIMENSION or expected_height > MAX_PIXEL_DIMENSION:
                print(f"Размеры изображения ({expected_width}x{expected_height}) превышают лимит")
                if current_dpi <= MIN_DPI:
                    break
                current_dpi -= DPI_STEP
                current_dpi = max(current_dpi, MIN_DPI)
                continue

            pix = page.get_pixmap(matrix=mat)
            pix.save(temp_file_path)
            file_size_mb = os.path.getsize(temp_file_path) / (1024 * 1024)

            print(f"DPI: {current_dpi}, размер: {expected_width}x{expected_height}, файл: {file_size_mb:.2f} МБ")

            if file_size_mb > MAX_IMAGE_SIZE_MB:
                if current_dpi <= MIN_DPI:
                    break
                # уменьшение DPI
                direction = "decrease"
                current_dpi -= DPI_STEP
                current_dpi = max(current_dpi, MIN_DPI)
            elif (
                    file_size_mb < MAX_IMAGE_SIZE_MB * 0.8 and
                    current_dpi + DPI_STEP <= MAX_DPI and
                    current_dpi + DPI_STEP <= max_dpi_for_dimensions and
                    direction != "decrease"
            ):
                # временно сохраняем, чтобы отменить при необходимости
                previous_dpi = current_dpi
                current_dpi += DPI_STEP

                # Проверяем размеры для нового DPI
                expected_width_new = int(page_width * current_dpi / 72)
                expected_height_new = int(page_height * current_dpi / 72)

                if expected_width_new > MAX_PIXEL_DIMENSION or expected_height_new > MAX_PIXEL_DIMENSION:
                    # Размеры превысят лимит, остаемся на предыдущем значении
                    current_dpi = previous_dpi
                    break

                # попробуем снова с увеличенным DPI
                mat = fitz.Matrix(current_dpi / 72, current_dpi / 72)
                pix = page.get_pixmap(matrix=mat)
                pix.save(temp_file_path)
                file_size_mb = os.path.getsize(temp_file_path) / (1024 * 1024)

                if file_size_mb > MAX_IMAGE_SIZE_MB:
                    # превышение — возвращаемся на предыдущее значение и пересохраняем
                    current_dpi = previous_dpi
                    mat = fitz.Matrix(current_dpi / 72, current_dpi / 72)
                    pix = page.get_pixmap(matrix=mat)
                    pix.save(temp_file_path)
                    break
            else:
                break

        doc.close()

        # Финальная проверка и вывод информации
        final_size_mb = os.path.getsize(temp_file_path) / (1024 * 1024)
        final_width = int(page_width * current_dpi / 72)
        final_height = int(page_height * current_dpi / 72)

        print(f"Финальные параметры: DPI={current_dpi}, размер={final_width}x{final_height}, файл={final_size_mb:.2f} МБ")

        return temp_file_path

    # Выполняем CPU-интенсивную операцию в отдельном потоке
    return await asyncio.to_thread(_process_page)


async def extract_pages_from_pdf(pdf_or_image_path, page_numbers: list[int] = None):
    """
    Извлекает данные из всех страниц PDF файла или указанной страницы или изображения асинхронно.
    """
    if not await asyncio.to_thread(exists, pdf_or_image_path):
        raise ValueError(f"File {pdf_or_image_path} does not exist.")

    data_all = []
    if page_numbers:
        # Обрабатываем указанные страницы параллельно
        tasks = [process_image(pdf_or_image_path, page_number) for page_number in page_numbers]
        data_all = await asyncio.gather(*tasks)
        doc_all = {'doc': data_all}
    else:
        # Открываем документ для получения количества страниц
        def _get_page_count():
            doc = fitz.open(pdf_or_image_path)
            page_count = len(doc)
            doc.close()
            return page_count

        page_count = await asyncio.to_thread(_get_page_count)
        
        # Обрабатываем все страницы параллельно (можно ограничить количество одновременных задач)
        semaphore = asyncio.Semaphore(5)  # Максимум 5 страниц одновременно
        
        async def process_with_semaphore(page_num):
            async with semaphore:
                return await process_image(pdf_or_image_path, page_num + 1)
        
        tasks = [process_with_semaphore(page_number) for page_number in range(page_count)]
        data_all = await asyncio.gather(*tasks)
        doc_all = {'doc': data_all}
    
    return doc_all


async def add_to_db_async(data):
    """
    Добавляет данные в базу данных асинхронно.
    """
    connection_params = {
        'host': os.getenv("PG_HOST_LOCAL", ""),
        'database': os.getenv("PG_DBNAME", ""),
        'user': os.getenv("PG_USER", ""),
        'password': os.getenv("PG_PASSWORD", ""),
        'port': int(os.getenv("PG_PORT", ""))
    }
    
    sql = """
        INSERT INTO t_scan_documents_raw
        (
            full_path,
            page_number,
            description,
            created_at
        )
        VALUES ($1, $2, $3, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET
            description = EXCLUDED.description,
            page_number = EXCLUDED.page_number,
            full_path = EXCLUDED.full_path,
            created_at = now()
        """
    
    values = []
    for doc in data:
        values.append((
            doc['full_path'],
            doc['page_number'],
            doc['description']
        ))
    
    try:
        conn = await asyncpg.connect(**connection_params)
        try:
            await conn.executemany(sql, values)
            print(f"Данные OCR успешно занесены в базу")
            return len(values)
        finally:
            await conn.close()
    except Exception as e:
        print(f"Ошибка при добавлении данных: {e}")
        return 0


async def process_image(pdf_or_image_path, page_number):
    """
    Обрабатывает изображение асинхронно, извлекая текст и данные с помощью различных методов OCR.
    """
    # Сохраняем страницу как изображение с высоким разрешением
    if get_file_extension(pdf_or_image_path) in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        temp_file_name = pdf_or_image_path
        should_remove_temp = False
    elif get_file_extension(pdf_or_image_path) == 'pdf':
        temp_file_name = await save_pdf_page_as_image(pdf_or_image_path, page_number)
        should_remove_temp = True
    else:
        raise ValueError(f"Не поддерживаемый формат файла для OCR: {get_file_extension(pdf_or_image_path)}")
    
    try:
        # Пробуем извлечь данные разными методами асинхронно
        ocr_text = await extract_ocr_data_by_mistral_async(temp_file_name)  # Качество хорошее. 1000 стр = 1 USD
        
        if ocr_text is None or ocr_text.strip() == "":
            # Если Mistral не сработал, пробуем Google Document AI
            ocr_text = await google_ocr_documentai_async(temp_file_name)  # Качество хорошее. 1000 стр = 1.5 USD

        data = {
            'full_path': pdf_or_image_path,
            'page_number': page_number,
            'description': ocr_text.strip() if ocr_text is not None else None
        }
        
        get_data = {}
        if ocr_text is not None and ocr_text.strip() != "":
            # Можно добавить асинхронную обработку данных здесь
            # get_data = await extract_data_by_grok(ocr_text)  # качество GROK - хорошее.
            # get_data = await extract_data_by_deepseek(ocr_text) # качество DeepSeek - хорошее.
            
            if isinstance(get_data, dict) and 'doc' in get_data and get_data['doc']:
                doc_data = get_data.get('doc')
                if doc_data and isinstance(doc_data, list):
                    get_data = doc_data[0]
                else:
                    get_data = data
                data = {**data, **get_data}
        
        # Добавляем данные в базу данных асинхронно
        dat_list = [data]
        await add_to_db_async(dat_list)
        
        return data
        
    finally:
        # Удаляем временный файл если он был создан
        if should_remove_temp and os.path.exists(temp_file_name):
            await asyncio.to_thread(os.remove, temp_file_name)


async def extract_pdf_files_from_folder(folder_path):
    """
    Извлекает PDF файлы из указанной папки и обрабатывает их асинхронно.
    """
    def _get_pdf_files():
        pdf_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        return pdf_files
    
    pdf_files = await asyncio.to_thread(_get_pdf_files)
    
    # Обрабатываем файлы параллельно с ограничением
    semaphore = asyncio.Semaphore(3)  # Максимум 3 файла одновременно
    
    async def process_file_with_semaphore(pdf_file):
        async with semaphore:
            return await extract_pages_from_pdf(pdf_file)
    
    tasks = [process_file_with_semaphore(pdf_file) for pdf_file in pdf_files]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Фильтруем ошибки
    successful_results = [r for r in results if not isinstance(r, Exception)]
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"Ошибка при обработке файла {pdf_files[i]}: {result}")
    
    return successful_results


async def get_small_data_from_db():
    """
    Отбираем записи из бд асинхронно, у которых количество символов после OCR распознавания < 1000.
    Повторно передаем их на повторное OCR распознавание.
    """
    sql = """    
        SELECT
            file_name,
            page_number
        FROM t_scan_documents_raw
        WHERE id NOT IN (1426, 1491)  -- печать, и пустая. их не обрабатывать
            AND id IN (
                SELECT external_id 
                FROM t_scan_documents 
                WHERE buyer_code = '41098985'
                    OR doc_type IS NULL
                    OR ((buyer_name IS NULL) AND (buyer_code IS NULL))
                )
        ORDER BY
            file_name,
            page_number
        ;
    """
    connection_params = {
        'host': os.getenv("PG_HOST_LOCAL", ""),
        'database': os.getenv("PG_DBNAME", ""),
        'user': os.getenv("PG_USER", ""),
        'password': os.getenv("PG_PASSWORD", ""),
        'port': int(os.getenv("PG_PORT", ""))
    }
    
    conn = await asyncpg.connect(**connection_params)
    try:
        all_rows = await conn.fetch(sql)
        
        # Обрабатываем записи параллельно
        semaphore = asyncio.Semaphore(5)  # Максимум 5 записей одновременно
        
        async def process_row_with_semaphore(row):
            async with semaphore:
                pdf_or_image_path = os.path.join(r"c:\Scan\All\ForParse", row['file_name'])
                page_number = row['page_number']
                print(pdf_or_image_path, page_number)
                return await extract_pages_from_pdf(pdf_or_image_path, [page_number])
        
        tasks = [process_row_with_semaphore(row) for row in all_rows]
        await asyncio.gather(*tasks, return_exceptions=True)
        
    finally:
        await conn.close()


async def main():
    """Главная асинхронная функция"""
    # Очистка экрана
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # Пример использования
    folder_path = r"\\PrestigeProduct\Блок 2024\Епіцентр\07_ЛИПЕНЬ 2024р"
    await extract_pdf_files_from_folder(folder_path)
    
    # Или обработка отдельного файла:
    # pdf_or_image_path = r"c:\Scan\All\ForParse\2025-05-31_1.pdf"
    # result = await extract_pages_from_pdf(pdf_or_image_path, [158, 224])
    # print(result)
    
    # Или повторная обработка из БД:
    # await get_small_data_from_db()


if __name__ == "__main__":
    asyncio.run(main())