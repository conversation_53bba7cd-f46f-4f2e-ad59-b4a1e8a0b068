import os
import pytesseract
import cv2
import numpy as np
from pdf2image import convert_from_path
from PIL import Image


def is_blank_image(img_np: np.ndarray) -> bool:
    gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
    blur = cv2.GaussianBlur(gray, (5, 5), 0)
    _, thresh = cv2.threshold(blur, 200, 255, cv2.THRESH_BINARY)
    white_ratio = np.sum(thresh == 255) / thresh.size
    return white_ratio > 0.99


def correct_orientation(img_np: np.ndarray) -> np.ndarray:
    coords = np.column_stack(np.where(cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY) < 250))
    if coords.size == 0:
        return img_np
    angle = cv2.minAreaRect(coords)[-1]
    if angle < -45:
        angle = -(90 + angle)
    else:
        angle = -angle
    (h, w) = img_np.shape[:2]
    M = cv2.getRotationMatrix2D((w // 2, h // 2), angle, 1.0)
    rotated = cv2.warpAffine(img_np, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
    return rotated


def process_pdf(pdf_path: str, output_md: str):
    try:
        images = convert_from_path(pdf_path, dpi=300)
    except Exception as e:
        print(f"Помилка при конвертації PDF: {e}")
        return

    lines = [f"# Розпізнаний текст з {os.path.basename(pdf_path)}"]
    for i, page in enumerate(images, 1):
        img = np.array(page.convert('RGB'))
        if is_blank_image(img):
            lines.append(f"\n## Сторінка {i}: порожня або біла")
            continue
        img = correct_orientation(img)
        try:
            config = "--oem 3 --psm 6 -l ukr"
            text = pytesseract.image_to_string(img, config=config).strip()
        except Exception as e:
            lines.append(f"\n## Сторінка {i}: помилка OCR - {e}")
            continue

        if not text:
            lines.append(f"\n## Сторінка {i}: текст не розпізнано")
        else:
            lines.append(f"\n## Сторінка {i}\n```\n{text}\n```")

    try:
        with open(output_md, 'w', encoding='utf-8') as f:
            f.write("\n".join(lines))
        print(f"Готово! Результат збережено в {output_md}")
    except Exception as e:
        print(f"Не вдалося зберегти Markdown файл: {e}")


if __name__ == "__main__":
    input_pdf = "ScanForTreaningNew.pdf"
    output_md = "ScanForTreaningNew.md"
    process_pdf(input_pdf, output_md)
