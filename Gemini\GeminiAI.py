# To run this code you need to install the following dependencies:
# pip install google-genai
# pip install --upgrade google-generativeai
# программа создана с помощью https://aistudio.google.com/prompts/1VvAiD-oYWC3g94h0wTOUMvzU7BpKrgn_

import base64
import os
from google import genai
from google.genai import types
from typing import Dict, Any, Union
import json


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]
                
            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def generate(pdf_path: str):
    """Generate a response from the model."""
    
    pdf_decoded = encode_pdf(pdf_path)
    if pdf_decoded is None:
        return None
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-pro-preview-05-06"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text= "file_name: " + os.path.basename(pdf_path) + """
Текст на украинском языке. Твоя задача извлечь:
1) тип документа. \\\"Товарно-Транспортная\\\" (ТТН), \\\"Видаткова накладна\\\" (ВН), Акт, \\\"Повернення посточальнику\\\" (ПП), Не смог определить - \\\"Другой\\\".
2) Наименование клиента/покупателя. Без Правового статуса. Имя клиента полностью, большими буквами. Данные продавца игнорируй.
3) код покупателя // для уникальности клиента.
4) страницы, которые относятся к данному документу
5) определи где первая страница, где середина и где последняя страница
6) страницы у тебя повторяться не должны. Одна страница относится только к одному документу.
7) номера документов, на которые ссылается ТТН.
8) Если у ТТН нет даты и она ссылается на несколько ВН с разными датами, бери самую позднюю или если все даты совпадают бери любую.
9) Укажи сумму с НДС. Для ВН она на последней странице(если много страниц). Для ТТН сумма с НДС указана на первой странице. Пишется прописью
10) Если код клиента одинаковый, наименования верни одинаковыми - верхний регистр.
11) колонка № номера строк (если есть), указанные в порядке возрастания
12) сложи все суммы с НДС у ВН, на которые ссылается ТТН и сравни с суммой указанной в ТТН. 

Для этого ты должен очень углубленно вникнуть в каждую деталь. Найти по каким ключевым данным определить какую страницу объединять с каким документом.
верни в формате валидного json:
{doc: [
file_name: \"ScanForTreaningNew.pdf\", // Не меняй.
doc_type: string, // тип документа: "ТТН", "ВН", "ПП", "Акт", "Другой". Коротко
doc_date: date | null // дата в формате dd.mm.yyyy
doc_number: numeric // число, без букв
buyer_name string // имя клиента, Иванов И. Эпицентр К. Правовой статус игнорируй. Коротко, без кавычек.
buyer_code numeric // 8 знаков, число
page_numbers: [numeric] //  [8,4,7,3] номера страниц сортируй первая страница, середина, последняя страница. Не по возрастанию номеров страниц - а логически. Если середина документа состоит из нескольких страниц, тогда при сортировке страниц воспользуйся номерами строк - rows_list.
invoice_numbers: [numeric] // номера документов ВН на которые ссылается ТТН. По возрастанию
amount_with_vat:  numeric(15,2) // сумма с НДС число,
rows_list: [numeric] // номера строк по возрастанию [1,2,3]
diff_amount_with_vat:numeric  // сумма расхождения. сумма ТТТН - (сумма ВН1 + сумма ВН2...)
]
}
*****
Ты должен дать себе перепроверить и дать себе ответ. Например:
1) как определил что страницы 10 и 9 относятся к одному и тому же документу.
2) как определил что сначала надо ставить страницу 10, потом 9.
3) как смог определить дату 21.09.2024 у документа. В документе даты не было.
4) сложил все суммы по ВН и сравнил с суммой, на которые ссылается ТТН - результат не сошелся. перепроверил ли страницы?
Ты включил абсолютно все свои профессиональные аналитические возможности и перепроверил свои данные углубляясь в каждую деталь и используя различные подходы, методы и переспрашивая себя а вдруг неправильно, ты нашел разные варианты как обосновать свой результат. Ты смог доказать, что ты - профессионал!

"""),
            ],
        ),
        types.Content(
            role="model",
            parts=[
                types.Part.from_text(text="""**Defining Extraction Strategy**

I'm currently developing a plan to extract key data from various accounting documents, beginning with \"Товарно-Транспортна накладна\". My focus is on identifying and classifying distinct document types within the multi-page file. This strategy is critical to avoid misinterpretation of data during extraction.


**Formulating Page Segmentation**

I've been refining my approach to page segmentation. I'm focusing on the distinct markers for \"Товарно-Транспортна накладна\" and \"Видаткова накладна.\" My latest plan involves iterating through each page, identifying key indicators like the specific phrases at the top, such as \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\" or \"Видаткова накладна\". I am also considering \"Зворотний бік\" to correctly connect the TTН's front and back pages. I aim to group pages for correct data extraction.


**Mapping Extraction Targets**

Now, I'm focusing on defining specific data fields to extract for each document type. For \"Товарно-Транспортна накладна,\" I'll target the document type, date, number, buyer name and code, and any referenced invoice numbers. For \"Видаткова накладна,\" the extraction process will mirror this, targeting similar key information. I'm prioritizing accuracy in matching fields and data validation checks to reduce errors. I am also planning on extracting the VAT amount, row numbers, and total amount, if present.


**Implementing Data Extraction**

I'm now focusing on the practical extraction logic. I'll initially prioritize \"Видаткова накладна\" (ВН) and \"Товарно-Транспортна накладна\" (TTН) document types. My strategy revolves around a page-by-page scan, identifying markers like \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\" for TTН and \"Видаткова накладна\" for ВН. Then, I will focus on extracting specific data from both document types.


**Implementing Logical Grouping**

I'm now focusing on implementing the page grouping logic. I've designed a system to identify and link \"Зворотний бік\" pages to their corresponding \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\" fronts. I'm also ready to handle multi-page invoices with \"Постачальник\" and \"Покупець\" on the initial page, along with finding \"Усього з ПДВ\" on the last page. My current focus is accurately identifying these document boundaries.


**Refining Data Extraction**

I've made substantial progress on refining data extraction. Currently, I'm focused on correctly matching document types and extracting pertinent information from each one, using \"Зворотний бік\" to correctly link TTН's front and back pages. In addition, I'm working on extracting and using dates to resolve uncertainties where they are not clearly specified, and planning the use of a stamp to help find hidden codes.


**Establishing Extraction Rules**

I've outlined specific extraction rules for each document type. For TTН, I'll extract date, number, buyer details, and invoice references. For ВН, it's the date, number, buyer information, and total amount. To clarify ambiguous dates, I'll prioritize identifying related invoices. Now, I will start by extracting the dates, names, codes, page numbers, and invoice numbers.


**Establishing Rule Implementation**

I've begun implementing the logic for extracting data based on the established extraction rules. I'm prioritizing the identification of distinct document types, specifically \"Видаткова накладна\" and \"Товарно-Транспортна накладна.\" To streamline this process, I'm focusing on \"Видаткова накладна\" first, then \"Товарно-Транспортна накладна.\" This strategy allows for a methodical approach to accurate extraction, using key markers like dates, document numbers, and buyer details, including any referenced invoices.


**Defining Data Extraction Logic**

I'm now focusing on implementing the identified logic. I'll prioritize identifying \"Видаткова накладна\" and \"Товарно-Транспортна накладна\" document types for processing. The strategy involves a page-by-page scan, using key indicators such as phrases like \"Видаткова накладна\" and \"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА\" to identify the document type and specific page numbers for extraction.


**Analyzing Document Structure**

I've successfully identified and grouped TTНs and ВНs within the provided documents. I've begun to extract key data points, including dates, numbers, buyer details, and invoice references. I'm focusing on handling missing dates and leveraging linked invoices to infer them.


**Implementing Data Validation**

I've made substantial progress in document extraction. I'm actively validating data fields against known patterns and expected formats, especially for date parsing. I'm focusing on \"Зворотний бік\" to correctly link TTН's front and back pages and ensure the integrity of the data. I'm now actively validating data, focusing on \"Видаткова накладна\" and \"Товарно-Транспортна накладна.\"


**Analyzing Structure & Refining Extraction**

I'm now refining the extraction logic, paying close attention to date discrepancies and inference based on linked documents. For example, dates for TTНs without explicit dates will be assigned if matching VN numbers are present. I'm also preparing for the incorporation of the \"stamp\" for more comprehensive data validation.


**Analyzing Data Validation**

I've been meticulously refining my extraction and validation processes. I'm focusing on \"Видаткова накладна\" and \"Товарно-Транспортна накладна\" to ensure accuracy in date extraction and correct inference. I'm also planning to integrate a more sophisticated validation strategy for extracted data, focusing on matching known patterns and expected data formats.


**Enhancing Data Validation**

I'm now refining data validation techniques, particularly for dates. I'm focusing on \"Видаткова накладна\" to ensure accurate date extraction and inferring dates for corresponding \"Товарно-Транспортна накладна.\"  My approach will include a combination of optical character recognition (OCR) and pattern matching to identify and validate dates, with a priority on consistency.


**Refining Date Extraction**

I'm now focusing on refining the date extraction process, particularly for TTНs. While explicit dates are preferred, I've implemented logic to infer dates using related ВНs when necessary, focusing on the latest date to ensure accuracy in cases of ambiguity.  I am also validating dates against established date patterns to maintain data integrity.


**Analyzing Data Consistency**

I've been meticulously refining my extraction and validation processes. I'm focusing on ensuring consistent handling of \"ЕПІЦЕНТР К\", identifying it, and accurately inferring missing dates by using linked documents. My next step will be to implement date validation, and ensure consistency between front and back pages.


**Finalizing Data Validation**

I'm now fully immersed in refining data validation, focusing on \"ЕПІЦЕНТР К\" consistency. I'm ensuring date inference accuracy with ВН documents for TTНs, utilizing both explicit and inferred dates. This approach minimizes discrepancies and guarantees data precision. I'm ready to proceed with more comprehensive validation.


**Implementing Data Validation**

I've been refining my validation strategy. It now includes comparing extracted data with expected patterns. For instance, dates extracted are validated against typical date formats. I'm focusing on \"Видаткова накладна\" (ВН) and \"Товарно-Транспортна накладна\" (ТТН) for data integrity.


**Finalizing Data Validation**

I'm now fully immersed in refining data validation, focusing on \"ЕПІЦЕНТР К\" consistency. I've ensured accurate date inference using associated \"Видаткова накладна\" for \"Товарно-Транспортна накладна.\" This precise approach minimizes inconsistencies, guaranteeing data integrity.


**Refining Data Integrity**

I'm now focusing on a complete review, verifying the overall document structure and accuracy. I'm focusing on \"Видаткова накладна\" (ВН) and \"Товарно-Транспортна накладна\" (TTН) documents for correct identification and data validation. The goal is to ensure data integrity and prepare for further analysis.


**Validating Data Consistency**

I'm now fully immersed in refining data validation, focusing on ensuring consistent handling of \"ЕПІЦЕНТР К\" across all document types. I've focused on data consistency to ensure accurate date inference with associated \"Видаткова накладна\" for \"Товарно-Транспортна накладна.\" This approach minimizes discrepancies, guaranteeing data integrity and a comprehensive validation check.


**Confirming Data Accuracy**

I've been working to ensure accuracy. I'm validating the extracted dates and invoice numbers, with the focus on \"Видаткова накладна\" to verify data integrity. I am also checking data from \"ЕПІЦЕНТР К\" for consistency. The final approach is to confirm data.


**Confirming Data Accuracy**

I'm now fully immersed in refining data validation, focusing on ensuring consistent handling of \"ЕПІЦЕНТР К\" and accurate date inference with associated \"Видаткова накладна\" for \"Товарно-Транспортна накладна.\" This precise approach minimizes inconsistencies and guarantees data accuracy. My final approach is to review all the documents.

"""),
            ],
        ),
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text="""INSERT_INPUT_HERE"""),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="text/plain",
    )

    response_text = ""
    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        response_text += chunk.text 
        
    cleared_response = clear_text(response_text)
    return cleared_response



def save_result_to_json(result:str, output_path: str) -> None:
    """Сохраняет результат анализа в JSON файл.

    Args:
        result: Словарь с результатами анализа
        output_path: Путь к файлу для сохранения
    """
    with open(output_path, "w", encoding="utf-8") as json_file:
        json.dump(result, json_file, ensure_ascii=False, indent=2)


def create_filename_from_json(json_data: Dict[str, Any]) -> str:
    buyer_name = json_data.get("buyer_name", "unknown").replace(" ","_").upper()
    buyer_code = json_data.get("buyer_code", "unknown")
    doc_type = json_data.get("doc_type", "unknown").replace(" ", "_").upper()
    doc_date = json_data.get("doc_date", "unknown")
    doc_number = json_data.get("doc_number", "unknown")
    page_numbers = json_data.get("page_numbers", [])
    if not isinstance(page_numbers, list):
        page_numbers = [page_numbers]  # Ensure page_numbers is a list
    folder_name = os.path.join(f"{buyer_code} {buyer_name}",doc_type)
    file_name = f"{buyer_code}_{buyer_name}_{doc_type}_{doc_date}_{doc_number}_pages_{'_'.join(map(str, page_numbers))}.json"
    return file_name


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
        
    pdf_path = r"d:\Scan\20250430_Merge 001-100.pdf"
    result = generate(pdf_path)
    base_name = str(pdf_path).replace(".pdf", ".json")
    # save_result_to_json(result,  base_name)
    save_result_to_json(result,  r"d:\Scan\20250430_Merge 001-100_1.json")
    print(result)
